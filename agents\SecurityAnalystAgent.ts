import { GeminiService } from '../services/geminiService';
import { GeminiJsonBugReportResponse, FileNode, LoggingInterface, AgentType } from '../types';

/**
 * SecurityAnalystAgent handles security vulnerability detection and analysis.
 * Provides comprehensive logging of all security analysis activities and decisions.
 */
export class SecurityAnalystAgent {
  private geminiService: GeminiService;
  private logger: LoggingInterface;

  constructor(geminiService: GeminiService, logger: LoggingInterface) {
    if (!geminiService) {
      throw new Error("SecurityAnalystAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("SecurityAnalystAgent: LoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Security Analyst Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry(AgentType.SECURITY_ANALYST, action, details, reason, taskId);
  }

  /**
   * Analyzes code for potential security vulnerabilities.
   * @param code - The source code to analyze.
   * @param filePath - The path of the file being analyzed.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the security vulnerability report response.
   */
  public async analyzeCodeForSecurityVulnerabilities(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonBugReportResponse> {
    try {
      await this.logActivity(`Starting security analysis for file: ${filePath} (${code.length} characters)`, 'working');
      this.logDecision('Security Analysis Started', `Analyzing ${filePath} for security vulnerabilities`, `Using model: ${modelName}`);

      const fileStructurePrompt = this.geminiService.serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        File Path Being Analyzed for Security: ${filePath}
        Code to analyze:
        \`\`\`
        ${code}
        \`\`\`
        Identify potential security vulnerabilities (e.g., XSS, SQLi, hardcoded secrets).
        Each vulnerability found MUST be returned as a BugInfo object with 'isSecurityIssue' set to true and appropriate 'severity' (usually 'high' or 'critical').
      `;

      const response = await this.geminiService.makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Security Analyst Agent. Analyze the provided code for potential security vulnerabilities. Return a JSON object with 'bugs' array containing security issue objects with 'filePath', 'bugId', 'description', 'severity', and 'isSecurityIssue' set to true.",
        (data: any): data is GeminiJsonBugReportResponse => {
          return typeof data === 'object' && data !== null &&
                 'bugs' in data && Array.isArray(data.bugs) &&
                 data.bugs.every((b: any) =>
                   typeof b === 'object' && b !== null &&
                   'filePath' in b && typeof b.filePath === 'string' &&
                   'bugId' in b && typeof b.bugId === 'string' &&
                   'description' in b && typeof b.description === 'string' &&
                   'severity' in b && typeof b.severity === 'string' &&
                   ['low', 'medium', 'high', 'critical'].includes(b.severity) &&
                   (b.isSecurityIssue === true)
                 );
        },
        0.4
      );

      await this.logActivity(`Security analysis completed for ${filePath}. Found ${response.bugs.length} security vulnerabilities`, response.bugs.length > 0 ? 'error' : 'success');
      this.logDecision('Security Analysis Completed', `Found ${response.bugs.length} security vulnerabilities in ${filePath}`, `Analysis completed successfully`);

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to analyze ${filePath} for security vulnerabilities: ${errorMessage}`, 'error');
      this.logDecision('Security Analysis Failed', `Error analyzing ${filePath}: ${errorMessage}`, 'Security analysis encountered an error');
      console.error(`SecurityAnalystAgent: Error analyzing code for security vulnerabilities in "${filePath}" -`, error);
      throw error;
    }
  }
}
